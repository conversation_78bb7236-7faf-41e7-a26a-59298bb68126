import { useEffect, useCallback } from 'react';
import { useIAP, initConnection } from 'expo-iap';
import { useAppStore } from '@/lib/store';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

// 产品ID - 需要与 useSubscription 中的保持一致
const PRODUCT_ID = 'rayboxui.pro';

/**
 * 订阅验证Hook接口
 */
export interface SubscriptionValidatorHook {
  validateExistingSubscription: () => Promise<void>;
  revalidateSubscription: () => Promise<void>;
  isConnected: boolean;
}

/**
 * 订阅验证Hook
 *
 * 用于在应用启动时检查已购买的订阅并验证其有效性
 * 支持iOS和Android平台的收据验证
 *
 * 功能：
 * - 自动检查已购买的订阅
 * - 验证收据有效性（iOS使用validateReceipt，Android依赖availablePurchases）
 * - 根据验证结果更新Pro状态
 * - 提供手动重新验证的方法
 */
export function useSubscriptionValidator(): SubscriptionValidatorHook {
  const { settings, updateSettings } = useAppStore();
  const {
    connected,
    availablePurchases,
    validateReceipt
  } = useIAP();

  /**
   * 验证已购买的订阅
   */
  const validateExistingSubscription = useCallback(async () => {
    if (!connected) {
      console.log('[SubscriptionValidator] IAP not connected, skipping validation');
      return;
    }

    // 添加防抖，避免与 useSubscription 冲突
    if (availablePurchases === undefined) {
      console.log('[SubscriptionValidator] Available purchases not yet loaded, skipping validation');
      return;
    }

    console.log('[SubscriptionValidator] Starting subscription validation');
    console.log('[SubscriptionValidator] Available purchases count:', availablePurchases?.length || 0);

    try {
      // 确保 IAP 连接已建立
      const isConnected = await initConnection();
      if (!isConnected) {
        console.error('[SubscriptionValidator] Failed to initialize IAP connection');
        return;
      }

      // 检查是否有可用的购买记录
      if (!availablePurchases || availablePurchases.length === 0) {
        console.log('[SubscriptionValidator] No available purchases found');

        // 只有在确认没有购买记录且当前状态是Pro时才更新状态
        // 添加额外的检查以避免与 useSubscription 冲突
        if (settings.isPro) {
          console.log('[SubscriptionValidator] No purchases found, but waiting to avoid conflict with useSubscription');
          // 延迟检查，给 useSubscription 时间处理
          setTimeout(async () => {
            const currentSettings = useAppStore.getState().settings;
            if (currentSettings.isPro && (!availablePurchases || availablePurchases.length === 0)) {
              console.log('[SubscriptionValidator] Delayed check: updating Pro status to false');
              await updateSettings({ isPro: false });
              await SecureStore.setItemAsync('isPro', 'false');
            }
          }, 2000);
        }
        return;
      }

      console.log('[SubscriptionValidator] Found', availablePurchases.length, 'available purchases');

      // 查找匹配的订阅产品
      const targetPurchase = availablePurchases.find(purchase => {
        // 检查多种可能的产品标识字段
        const productIdentifiers = [
          purchase.id,
          purchase.transactionReceipt
        ].filter(Boolean);

        return productIdentifiers.some(identifier =>
          identifier === PRODUCT_ID ||
          (typeof identifier === 'string' && identifier.includes(PRODUCT_ID))
        );
      });

      if (!targetPurchase) {
        console.log('[SubscriptionValidator] No matching subscription found for product:', PRODUCT_ID);
        
        // 如果没有找到匹配的订阅但当前状态是Pro，需要更新状态
        if (settings.isPro) {
          console.log('[SubscriptionValidator] No matching subscription, updating Pro status to false');
          await updateSettings({ isPro: false });
          await SecureStore.setItemAsync('isPro', 'false');
        }
        return;
      }

      console.log('[SubscriptionValidator] Found matching subscription, validating...');

      // 根据平台进行收据验证
      let isValidSubscription = false;

      if (Platform.OS === 'ios') {
        try {
          // iOS: 使用 validateReceipt 验证收据
          const receiptValidation = await validateReceipt(PRODUCT_ID);
          console.log('[SubscriptionValidator] iOS receipt validation result:', receiptValidation);
          isValidSubscription = receiptValidation && receiptValidation.isValid;
        } catch (validationError) {
          console.warn('[SubscriptionValidator] iOS receipt validation failed:', validationError);
          // 验证失败时，我们保守地认为订阅无效
          isValidSubscription = false;
        }
      } else if (Platform.OS === 'android') {
        // Android: 如果购买记录存在，通常表示订阅有效
        // 但我们可以进行额外的检查
        try {
          // 对于Android，我们主要依赖availablePurchases的存在
          // 因为Google Play会自动管理过期的订阅
          isValidSubscription = true;
          console.log('[SubscriptionValidator] Android subscription validated via available purchases');
        } catch (validationError) {
          console.warn('[SubscriptionValidator] Android validation failed:', validationError);
          isValidSubscription = false;
        }
      }

      // 根据验证结果更新Pro状态
      if (isValidSubscription && !settings.isPro) {
        console.log('[SubscriptionValidator] Valid subscription found, updating Pro status to true');
        await updateSettings({ isPro: true });
        await SecureStore.setItemAsync('isPro', 'true');
      } else if (!isValidSubscription && settings.isPro) {
        console.log('[SubscriptionValidator] Invalid subscription, updating Pro status to false');
        await updateSettings({ isPro: false });
        await SecureStore.setItemAsync('isPro', 'false');
      } else {
        console.log('[SubscriptionValidator] Pro status already correct:', settings.isPro);
      }

    } catch (error) {
      console.error('[SubscriptionValidator] Subscription validation failed:', error);
      
      // 验证过程出错时，我们保持当前状态不变
      // 但记录错误以便调试
    }
  }, [connected, availablePurchases, settings.isPro, updateSettings, validateReceipt]);

  /**
   * 手动触发订阅验证
   * 可以在需要时调用此方法重新验证订阅状态
   */
  const revalidateSubscription = useCallback(async () => {
    console.log('[SubscriptionValidator] Manual revalidation triggered');
    await validateExistingSubscription();
  }, [validateExistingSubscription]);

  // 当IAP连接建立且有可用购买记录时自动验证
  useEffect(() => {
    if (connected && availablePurchases !== undefined) {
      // 延迟执行验证，确保所有状态都已稳定
      const timer = setTimeout(() => {
        validateExistingSubscription();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [connected, availablePurchases, validateExistingSubscription]);

  return {
    validateExistingSubscription,
    revalidateSubscription,
    isConnected: connected
  };
}
