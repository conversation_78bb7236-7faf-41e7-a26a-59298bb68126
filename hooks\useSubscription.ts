import { useState, useCallback, useEffect } from 'react';
import { useIAP } from 'expo-iap';
import { useAppStore } from '@/lib/store';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';


// 产品ID - 需要在App Store Connect和Google Play Console中配置
const PRODUCT_ID = 'rayboxui.pro'; // 替换为实际的产品ID

/**
 * 订阅管理Hook
 *
 * 基于expo-iap的真实IAP实现，支持：
 * - 购买订阅产品
 * - 恢复购买
 * - 自动处理购买状态更新
 * - 错误处理和状态管理
 * - 购买验证和完成交易
 */

export interface SubscriptionHook {
  isPro: boolean;
  isLoading: boolean;
  isConnected: boolean;
  purchasePro: () => Promise<void>;
  restorePurchases: () => Promise<void>;
  error: string | null;
  products: any[];
  subscriptions: any[];
  currentPurchase: any;
}

export const useSubscription = (): SubscriptionHook => {
  const { settings, updateSettings } = useAppStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 使用expo-iap的useIAP hook
  const {
    connected,
    products,
    subscriptions,
    requestProducts,
    requestPurchase,
    restorePurchases: iapRestorePurchases,
    currentPurchase,
    currentPurchaseError,
    finishTransaction,
    availablePurchases,
    clearCurrentPurchase,
    clearCurrentPurchaseError,
    validateReceipt
  } = useIAP({
    onPurchaseSuccess: async (purchase) => {
      console.log('Purchase successful:', purchase);
      try {
        // 购买成功后验证收据
        let isValidPurchase = false;

        if (Platform.OS === 'ios') {
          // iOS: 使用 validateReceipt 验证收据
          try {
            const receiptValidation = await validateReceipt(PRODUCT_ID);
            console.log('iOS receipt validation result:', receiptValidation);
            isValidPurchase = receiptValidation && receiptValidation.isValid;
          } catch (validationError) {
            console.warn('iOS receipt validation failed:', validationError);
            // 即使验证失败，我们仍然信任购买成功的回调
            isValidPurchase = true;
          }
        } else {
          // Android: 购买成功回调本身就是验证
          isValidPurchase = true;
        }

        if (isValidPurchase) {
          // 验证成功，更新Pro状态
          await updateSettings({ isPro: true });
          await SecureStore.setItemAsync('isPro', 'true');
          console.log('Purchase verified and Pro status updated');
        } else {
          console.error('Purchase verification failed');
          setError('Purchase verification failed');
          return;
        }

        // 完成交易
        await finishTransaction({
          purchase,
          isConsumable: false // 订阅不是消耗品
        });

        setError(null);
      } catch (err) {
        console.error('Error processing successful purchase:', err);
        setError('Failed to process purchase');
      }
    },
    onPurchaseError: (purchaseError) => {
      console.error('Purchase error:', purchaseError);
      let errorMessage = 'Purchase failed';

      // 根据错误代码提供更友好的错误信息
      switch (purchaseError.code) {
        case 'E_USER_CANCELLED':
          errorMessage = 'Purchase was cancelled';
          break;
        case 'E_ITEM_UNAVAILABLE':
          errorMessage = 'Product is not available';
          break;
        case 'E_NETWORK_ERROR':
          errorMessage = 'Network error, please try again';
          break;
        case 'E_ALREADY_OWNED':
          errorMessage = 'You already own this subscription';
          // 如果已经拥有，更新状态
          updateSettings({ isPro: true });
          break;
        default:
          errorMessage = purchaseError.message || 'An unexpected error occurred';
      }

      setError(errorMessage);
    },
    shouldAutoSyncPurchases: true, // 自动同步购买状态
  });

  // 初始化时获取产品信息
  useEffect(() => {
    if (connected) {
      console.log('[useSubscription] Store connected, requesting products...');
      console.log('[useSubscription] Product ID to request:', PRODUCT_ID);
      // 获取订阅产品信息
      requestProducts({ skus: [PRODUCT_ID], type: 'subs' });
    } else {
      console.log('[useSubscription] Store not connected');
    }
  }, [connected, requestProducts]);

  // 监听产品加载结果
  useEffect(() => {
    if (connected) {
      console.log('[useSubscription] Products loaded:', products?.length || 0);
      console.log('[useSubscription] Subscriptions loaded:', subscriptions?.length || 0);

      if (products && products.length > 0) {
        console.log('[useSubscription] Available products:', products.map(p => ({
          id: p.id,
          title: p.title,
          price: p.price
        })));
      }

      if (subscriptions && subscriptions.length > 0) {
        console.log('[useSubscription] Available subscriptions:', subscriptions.map(s => ({
          id: s.id,
          title: s.title,
          price: s.price
        })));
      }

      // 检查目标产品是否存在
      const targetProduct = [...(products || []), ...(subscriptions || [])].find(p =>
        p.id === PRODUCT_ID
      );

      if (!targetProduct && (products?.length > 0 || subscriptions?.length > 0)) {
        console.warn('[useSubscription] Target product not found in available products/subscriptions');
        console.warn('[useSubscription] Available product IDs:',
          [...(products || []), ...(subscriptions || [])].map(p => p.id)
        );
      } else if (targetProduct) {
        console.log('[useSubscription] Target product found:', targetProduct);
      }
    }
  }, [connected, products, subscriptions]);

  // 检查现有购买状态
  useEffect(() => {
    if (availablePurchases && availablePurchases.length > 0) {
      console.log('[useSubscription] Available purchases:', availablePurchases.length);
      console.log('[useSubscription] Available purchases details:', availablePurchases.map(p => ({
        id: p.id,
        transactionReceipt: p.transactionReceipt ? 'Present' : 'Missing'
      })));

      // 修复：检查购买记录中的产品ID，而不是交易ID
      const hasProSubscription = availablePurchases.some(purchase => {
        // 根据 expo-iap 的类型定义，purchase 对象可能包含不同的产品标识字段
        // 我们需要检查可能的字段来找到产品ID
        const productIdentifiers = [          // 标准字段
          purchase.id,                  // 有些情况下id可能是产品ID               // 可能的SKU字段
          purchase.transactionReceipt   // 收据中可能包含产品信息
        ].filter(Boolean);

        console.log('[useSubscription] Checking purchase:', {
          transactionId: purchase.id,
          productIdentifiers,
          targetProductId: PRODUCT_ID
        });

        // 检查是否有任何标识符匹配我们的产品ID
        return productIdentifiers.some(identifier =>
          identifier === PRODUCT_ID ||
          (typeof identifier === 'string' && identifier.includes(PRODUCT_ID))
        );
      });

      if (hasProSubscription && !settings.isPro) {
        console.log('[useSubscription] Found valid Pro subscription, updating status');
        // 发现有效的订阅，更新状态
        updateSettings({ isPro: true });
        SecureStore.setItemAsync('isPro', 'true');
      }
    }
  }, [availablePurchases, settings.isPro, updateSettings]);

  // 处理当前购买错误
  useEffect(() => {
    if (currentPurchaseError) {
      console.log('[useSubscription] Purchase error:', currentPurchaseError);
      setIsLoading(false);
    }
  }, [currentPurchaseError]);

  // 处理当前购买成功
  useEffect(() => {
    if (currentPurchase) {
      console.log('[useSubscription] Purchase successful:', currentPurchase.id);
      setIsLoading(false);
    }
  }, [currentPurchase]);

  // 调试信息 - 订阅状态变化
  useEffect(() => {
    if (__DEV__) {
      console.log('[useSubscription] Debug Info:', {
        connected,
        isPro: settings.isPro,
        isLoading,
        productsCount: products.length,
        subscriptionsCount: subscriptions.length,
        error,
        hasCurrentPurchase: !!currentPurchase,
        availablePurchasesCount: availablePurchases.length
      });
    }
  }, [connected, settings.isPro, isLoading, products.length, subscriptions.length, error, currentPurchase, availablePurchases.length]);

  // 购买Pro版本
  const purchasePro = useCallback(async (): Promise<void> => {
    if (!connected) {
      console.log('[useSubscription] Purchase failed: Store not connected');
      setError('Store connection not available');
      return;
    }

    // 检查产品是否可用
    const targetProduct = [...(products || []), ...(subscriptions || [])].find(p =>
      p.id === PRODUCT_ID
    );

    if (!targetProduct) {
      console.error('[useSubscription] Product not found:', PRODUCT_ID);
      console.error('[useSubscription] Available products:',
        [...(products || []), ...(subscriptions || [])].map(p => p.id)
      );
      setError(`Product "${PRODUCT_ID}" not found. Please check product configuration.`);
      return;
    }

    console.log('[useSubscription] Starting purchase for product:', PRODUCT_ID);
    console.log('[useSubscription] Target product details:', {
      id: targetProduct.id,
      title: targetProduct.title,
      price: targetProduct.price
    });

    setIsLoading(true);
    setError(null);
    clearCurrentPurchase();
    clearCurrentPurchaseError();

    try {
      await requestPurchase({
        request: {
          ios: { sku: PRODUCT_ID },
          android: {
            skus: [PRODUCT_ID],
            subscriptionOffers: [{ sku: PRODUCT_ID, offerToken: '' }]
          }
        },
        type: 'subs'
      });
      console.log('[useSubscription] Purchase request sent successfully');
    } catch (err) {
      console.error('[useSubscription] Purchase request failed:', err);

      // 提供更详细的错误信息
      let errorMessage = 'Purchase failed';
      if (err instanceof Error) {
        errorMessage = err.message;

        // 针对常见错误提供更友好的提示
        if (err.message.includes('Invalid product ID')) {
          errorMessage = `Product "${PRODUCT_ID}" is not configured correctly. Please check App Store Connect or Google Play Console.`;
        } else if (err.message.includes('network')) {
          errorMessage = 'Network error. Please check your internet connection and try again.';
        }
      }

      setError(errorMessage);
      setIsLoading(false);
      throw err;
    }
  }, [connected, products, subscriptions, requestPurchase, clearCurrentPurchase, clearCurrentPurchaseError]);

  // 恢复购买
  const restorePurchases = useCallback(async (): Promise<void> => {
    if (!connected) {
      console.log('[useSubscription] Restore failed: Store not connected');
      setError('Store connection not available');
      return;
    }

    console.log('[useSubscription] Starting restore purchases');
    setIsLoading(true);
    setError(null);

    try {
      await iapRestorePurchases();
      console.log('[useSubscription] Purchases restored successfully');
    } catch (err) {
      console.error('[useSubscription] Restore purchases failed:', err);
      setError(err instanceof Error ? err.message : 'Restore failed');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [connected, iapRestorePurchases]);

  return {
    isPro: settings.isPro,
    isLoading,
    isConnected: connected,
    purchasePro,
    restorePurchases,
    error,
    products,
    subscriptions,
    currentPurchase,
  };
};
