import { DarkTheme, DefaultTheme, ThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack, useRouter } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import 'react-native-reanimated';

import '../global.css';

import { useColorScheme } from '@/hooks/useColorScheme';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppStore } from '@/lib/store';
import { useSubscription } from '@/hooks/useSubscription';


import React from 'react';
import { Label } from '~/components/ui/label';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { PortalHost } from '@rn-primitives/portal';
import { Platform } from 'react-native';
import { FullWindowOverlay } from 'react-native-screens';
import { KeyboardProvider } from "react-native-keyboard-controller";
import { initialWindowMetrics, SafeAreaProvider } from 'react-native-safe-area-context';

const WindowOverlay = Platform.OS === "ios" ? FullWindowOverlay : React.Fragment as any
// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const { loadData, isLoading } = useAppStore();
  const { t } = useTranslation();
  const router = useRouter();

  // 使用订阅hook，它现在包含了验证逻辑
  useSubscription();

  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  const handleCloseModel = () => {
    router.back();
  };

  useEffect(() => {
    // 加载应用数据
    loadData();
  }, [loadData]);

  useEffect(() => {
    if (loaded && !isLoading) {
     const timer = setTimeout(() => SplashScreen.hideAsync(), 200)
     return () => clearTimeout(timer)
    }
  }, [loaded, isLoading]);



  if (!loaded || isLoading) {
    return null;
  }

  return (
    <SafeAreaProvider initialMetrics={initialWindowMetrics}>
    <KeyboardProvider>
    <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
      <GestureHandlerRootView>
        <BottomSheetModalProvider>
      <Stack screenOptions={{headerShown:false}}>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen
          name="add-config"
          options={{
            presentation: 'modal',
            title: t('navigation.addConfig'),
            headerShown: true,
            headerLeft: () => <Label onPress={handleCloseModel}>{t('common.cancel')}</Label>,
          }}
        />
        <Stack.Screen
          name="edit-groups"
          options={{
            presentation: 'modal',
            title: t('groups.title'),
            headerShown: true,
            headerLeft: () => <Label onPress={handleCloseModel}>{t('common.cancel')}</Label>,
          }}
        />
        <Stack.Screen name='config-form' options={{presentation:'modal'}} />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
      </BottomSheetModalProvider>
      </GestureHandlerRootView>
      <WindowOverlay>
          <PortalHost />
      </WindowOverlay>
    </ThemeProvider>
    </KeyboardProvider>
    </SafeAreaProvider>
  );
}
